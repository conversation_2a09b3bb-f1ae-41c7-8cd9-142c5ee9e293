<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  layout: 'tabbar',
  style: {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    navigationStyle: 'custom',
    navigationBarTitleText: '首页',
  },
}
</route>

<script lang="ts" setup>
import PLATFORM from '@/utils/platform'

defineOptions({
  name: 'Home',
})

// 获取屏幕边界到安全区域距离
let safeAreaInsets
let systemInfo

// #ifdef MP-WEIXIN
// 微信小程序使用新的API
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
// 其他平台继续使用uni API
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif
const author = ref('菲鸽')
const description = ref(
  'unibest 是一个集成了多种工具和技术的 uniapp 开发模板，由 uniapp + Vue3 + Ts + Vite5 + UnoCss + VSCode 构建，模板具有代码提示、自动格式化、统一配置、代码片段等功能，并内置了许多常用的基本组件和基本功能，让你编写 uniapp 拥有 best 体验。',
)
// 测试 uni API 自动引入
onLoad(() => {
  console.log('项目作者:', author.value)
})

console.log('index')

// 跳转到金价页面
function goToGoldInfo() {
  uni.navigateTo({
    url: '/pages/gold/index',
  })
}
</script>

<template>
  <view class="bg-white px-4 pt-2" :style="{ marginTop: `${safeAreaInsets?.top}px` }">
    <view class="mt-10">
      <image src="/static/logo.svg" alt="" class="mx-auto block h-28 w-28" />
    </view>
    <view class="mt-4 text-center text-4xl text-[#d14328]">
      unibest
    </view>
    <view class="mb-8 mt-2 text-center text-2xl">
      最好用的 uniapp 开发模板
    </view>

    <view class="m-auto mb-2 max-w-100 text-justify indent text-4">
      {{ description }}
    </view>
    <view class="mt-4 text-center">
      作者：
      <text class="text-green-500">
        菲鸽
      </text>
    </view>
    <view class="mt-4 text-center">
      官网地址：
      <text class="text-green-500">
        https://unibest.tech
      </text>
    </view>
    <!-- 金价实时行情入口 -->
    <view class="gold-entry" @click="goToGoldInfo">
      <view class="gold-icon">
        <text class="icon-text">
          💰
        </text>
      </view>
      <view class="gold-info">
        <text class="gold-title">
          金价实时行情
        </text>
        <text class="gold-desc">
          查看实时金价、K线图表、AI智能分析
        </text>
      </view>
      <view class="arrow">
        <text class="arrow-text">
          →
        </text>
      </view>
    </view>

    <view class="mt-6 h-1px bg-#eee" />
    <view class="mt-8 text-center">
      当前平台是：
      <text class="text-green-500">
        {{ PLATFORM.platform }}
      </text>
    </view>
    <view class="mt-4 text-center">
      模板分支是：
      <text class="text-green-500">
        base
      </text>
    </view>
  </view>
</template>

<style scoped>
.gold-entry {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #ffc107 0%, #ffed4e 100%);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(255, 193, 7, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  margin: 30rpx 0;
}

.gold-entry:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(255, 193, 7, 0.2);
}

.gold-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-text {
  font-size: 40rpx;
}

.gold-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.gold-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.gold-desc {
  font-size: 24rpx;
  color: #666;
  opacity: 0.8;
}

.arrow {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
</style>
