<template>
  <view class="gold-price-test">
    <view class="header">
      <text class="title">金价数据测试</text>
    </view>

    <view class="section">
      <button @click="testGoldPrices" :disabled="loading">
        {{ loading ? '加载中...' : '获取金价数据' }}
      </button>
    </view>

    <view v-if="error" class="error">
      <text>错误: {{ error }}</text>
    </view>

    <view v-if="goldPrices.length" class="data-section">
      <view class="section-title">金价数据 ({{ goldPrices.length }} 条)</view>
      <view v-for="(item, index) in goldPrices" :key="index" class="price-card">
        <view class="card-header">
          <text class="name">{{ item.name }}</text>
          <text class="type">{{ item.type }}</text>
        </view>
        
        <view class="price-info">
          <view class="main-price">
            <text class="current-price">¥{{ item.currentPrice }}</text>
            <text class="change">{{ calculateChange(item.currentPrice, item.previousSettlement) }}</text>
          </view>
          
          <view class="price-grid">
            <view class="price-item">
              <text class="label">买入价</text>
              <text class="value">¥{{ item.buyPrice }}</text>
            </view>
            <view class="price-item">
              <text class="label">卖出价</text>
              <text class="value">¥{{ item.sellPrice }}</text>
            </view>
            <view class="price-item">
              <text class="label">最高价</text>
              <text class="value">¥{{ item.high }}</text>
            </view>
            <view class="price-item">
              <text class="label">最低价</text>
              <text class="value">¥{{ item.low }}</text>
            </view>
            <view class="price-item">
              <text class="label">开盘价</text>
              <text class="value">¥{{ item.open }}</text>
            </view>
            <view class="price-item">
              <text class="label">前结算</text>
              <text class="value">¥{{ item.previousSettlement }}</text>
            </view>
          </view>
        </view>
        
        <view class="time-info">
          <text class="update-time">更新时间: {{ item.updateTime }}</text>
          <text class="create-time">{{ item.createTime }}</text>
        </view>
      </view>
    </view>

    <view v-if="rawData" class="raw-data-section">
      <view class="section-title">原始数据</view>
      <view class="raw-data">
        <text>{{ JSON.stringify(rawData, null, 2) }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { goldApi } from '@/api/gold'
import type { GoldPriceData } from '@/api/gold'

const loading = ref(false)
const error = ref('')
const goldPrices = ref<GoldPriceData[]>([])
const rawData = ref<any>(null)

// 计算涨跌幅
function calculateChange(current: string, previous: string): string {
  const currentNum = Number.parseFloat(current)
  const previousNum = Number.parseFloat(previous)
  
  if (Number.isNaN(currentNum) || Number.isNaN(previousNum) || previousNum === 0) {
    return '0.00 (0.00%)'
  }
  
  const change = currentNum - previousNum
  const changePercent = (change / previousNum) * 100
  const sign = change > 0 ? '+' : ''
  const color = change > 0 ? 'up' : change < 0 ? 'down' : 'neutral'
  
  return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`
}

// 测试金价API
async function testGoldPrices() {
  loading.value = true
  error.value = ''
  goldPrices.value = []
  rawData.value = null
  
  try {
    console.log('开始获取金价数据...')
    const result = await goldApi.getGoldPrices()
    
    console.log('API返回结果:', result)
    rawData.value = result
    
    if (Array.isArray(result)) {
      goldPrices.value = result
      console.log('金价数据解析成功:', goldPrices.value)
    } else {
      throw new Error('返回数据格式不正确，期望数组格式')
    }
    
    uni.showToast({
      title: `获取成功 (${goldPrices.value.length}条)`,
      icon: 'success',
    })
  } catch (err) {
    console.error('获取金价数据失败:', err)
    error.value = String(err)
    
    uni.showToast({
      title: '获取失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.gold-price-test {
  padding: 20rpx;
  
  .header {
    text-align: center;
    margin-bottom: 30rpx;
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .section {
    margin-bottom: 30rpx;
    
    button {
      width: 100%;
      padding: 24rpx;
      background: #007aff;
      color: white;
      border: none;
      border-radius: 12rpx;
      font-size: 32rpx;
      
      &:disabled {
        background: #ccc;
      }
    }
  }
  
  .error {
    padding: 20rpx;
    background: #ffebee;
    border-radius: 8rpx;
    margin-bottom: 20rpx;
    
    text {
      color: #d32f2f;
      font-size: 28rpx;
    }
  }
  
  .data-section {
    margin-bottom: 30rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }
  }
  
  .price-card {
    background: white;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    
    .card-header {
      margin-bottom: 16rpx;
      
      .name {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 4rpx;
      }
      
      .type {
        font-size: 24rpx;
        color: #666;
      }
    }
    
    .price-info {
      margin-bottom: 16rpx;
      
      .main-price {
        display: flex;
        align-items: baseline;
        justify-content: space-between;
        margin-bottom: 16rpx;
        
        .current-price {
          font-size: 36rpx;
          font-weight: bold;
          color: #333;
        }
        
        .change {
          font-size: 24rpx;
          color: #666;
        }
      }
      
      .price-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12rpx;
        
        .price-item {
          display: flex;
          justify-content: space-between;
          padding: 8rpx 12rpx;
          background: #f8f9fa;
          border-radius: 6rpx;
          
          .label {
            font-size: 24rpx;
            color: #666;
          }
          
          .value {
            font-size: 24rpx;
            color: #333;
            font-weight: 500;
          }
        }
      }
    }
    
    .time-info {
      border-top: 1rpx solid #eee;
      padding-top: 12rpx;
      
      .update-time, .create-time {
        font-size: 22rpx;
        color: #999;
        display: block;
        margin-bottom: 4rpx;
      }
    }
  }
  
  .raw-data-section {
    .section-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 16rpx;
    }
    
    .raw-data {
      background: #f5f5f5;
      padding: 20rpx;
      border-radius: 8rpx;
      
      text {
        font-family: monospace;
        font-size: 22rpx;
        color: #333;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }
}
</style>
