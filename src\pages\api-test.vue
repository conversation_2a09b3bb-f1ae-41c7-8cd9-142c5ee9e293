<template>
  <view class="api-test">
    <view class="header">
      <text class="title">API 地址测试</text>
    </view>

    <view class="section">
      <view class="section-title">环境信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="label">开发环境:</text>
          <text class="value">{{ envInfo.isDev ? '是' : '否' }}</text>
        </view>
        <view class="info-item">
          <text class="label">代理启用:</text>
          <text class="value">{{ envInfo.proxyEnabled ? '是' : '否' }}</text>
        </view>
        <view class="info-item">
          <text class="label">代理前缀:</text>
          <text class="value">{{ envInfo.proxyPrefix }}</text>
        </view>
        <view class="info-item">
          <text class="label">服务器地址:</text>
          <text class="value">{{ envInfo.serverBaseUrl }}</text>
        </view>
        <view class="info-item">
          <text class="label">最终API地址:</text>
          <text class="value">{{ envInfo.finalApiUrl }}</text>
        </view>
      </view>
    </view>

    <view class="section">
      <view class="section-title">测试请求</view>
      <button @click="testApiRequest" :disabled="loading">
        {{ loading ? '请求中...' : '测试API请求' }}
      </button>
      
      <view v-if="requestInfo.url" class="request-info">
        <view class="info-item">
          <text class="label">请求URL:</text>
          <text class="value">{{ requestInfo.url }}</text>
        </view>
        <view class="info-item">
          <text class="label">请求状态:</text>
          <text :class="['value', requestInfo.success ? 'success' : 'error']">
            {{ requestInfo.success ? '成功' : '失败' }}
          </text>
        </view>
        <view v-if="requestInfo.error" class="info-item">
          <text class="label">错误信息:</text>
          <text class="value error">{{ requestInfo.error }}</text>
        </view>
      </view>
    </view>

    <view class="section">
      <view class="section-title">控制台日志</view>
      <text class="tip">请打开浏览器控制台查看详细的调试信息</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { goldApi } from '@/api/gold'

// 环境信息
const envInfo = reactive({
  isDev: false,
  proxyEnabled: false,
  proxyPrefix: '',
  serverBaseUrl: '',
  finalApiUrl: '',
})

// 请求信息
const requestInfo = reactive({
  url: '',
  success: false,
  error: '',
})

const loading = ref(false)

// 获取环境信息
const getEnvInfo = () => {
  envInfo.isDev = import.meta.env.DEV
  envInfo.proxyEnabled = import.meta.env.VITE_APP_PROXY === 'true'
  envInfo.proxyPrefix = import.meta.env.VITE_APP_PROXY_PREFIX || ''
  envInfo.serverBaseUrl = import.meta.env.VITE_SERVER_BASEURL || ''
  
  // 计算最终API地址
  // #ifdef H5
  if (envInfo.isDev && envInfo.proxyEnabled) {
    envInfo.finalApiUrl = envInfo.proxyPrefix
  } else {
    envInfo.finalApiUrl = envInfo.serverBaseUrl
  }
  // #endif
  
  // #ifndef H5
  envInfo.finalApiUrl = envInfo.serverBaseUrl
  // #endif
  
  console.log('页面环境信息:', envInfo)
}

// 测试API请求
const testApiRequest = async () => {
  loading.value = true
  requestInfo.url = ''
  requestInfo.success = false
  requestInfo.error = ''
  
  try {
    console.log('开始测试API请求...')
    
    // 构建测试URL
    const testUrl = `${envInfo.finalApiUrl}/open/gold/openGoldPricesController/`
    requestInfo.url = testUrl
    
    console.log('测试请求URL:', testUrl)
    
    // 发起请求
    const result = await goldApi.getGoldPrices()
    
    console.log('API请求成功:', result)
    requestInfo.success = true
    
    uni.showToast({
      title: '请求成功',
      icon: 'success',
    })
  } catch (error) {
    console.error('API请求失败:', error)
    requestInfo.success = false
    requestInfo.error = String(error)
    
    uni.showToast({
      title: '请求失败',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getEnvInfo()
})
</script>

<style lang="scss" scoped>
.api-test {
  padding: 20rpx;
  
  .header {
    text-align: center;
    margin-bottom: 40rpx;
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .section {
    margin-bottom: 40rpx;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    button {
      width: 100%;
      padding: 20rpx;
      background: #007aff;
      color: white;
      border: none;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin-bottom: 20rpx;
      
      &:disabled {
        background: #ccc;
      }
    }
    
    .tip {
      color: #666;
      font-size: 26rpx;
      font-style: italic;
    }
  }
  
  .info-list, .request-info {
    .info-item {
      display: flex;
      margin-bottom: 16rpx;
      padding: 12rpx;
      background: white;
      border-radius: 8rpx;
      
      .label {
        font-weight: bold;
        color: #333;
        min-width: 200rpx;
        flex-shrink: 0;
      }
      
      .value {
        color: #666;
        flex: 1;
        word-break: break-all;
        
        &.success {
          color: #34c759;
        }
        
        &.error {
          color: #ff3b30;
        }
      }
    }
  }
}
</style>
