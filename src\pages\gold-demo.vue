<script lang="ts" setup>
import type { GoldPriceData, KLineData, TimeData } from '@/api/gold'
import { reactive, ref } from 'vue'
import { goldApi } from '@/api/gold'

// 响应式数据
const goldPrices = ref<GoldPriceData[]>([])
const klineData = ref<KLineData[]>([])
const timeData = ref<TimeData[]>([])
const technicalData = ref<any>(null)
const newsData = ref<any[]>([])
const error = ref<string>('')

// 加载状态
const loading = reactive({
  prices: false,
  kline: false,
  time: false,
  technical: false,
  news: false,
})

// 计算涨跌幅
function calculateChange(current: string, previous: string): string {
  const currentNum = Number.parseFloat(current)
  const previousNum = Number.parseFloat(previous)

  if (Number.isNaN(currentNum) || Number.isNaN(previousNum) || previousNum === 0) {
    return '0.00%'
  }

  const change = currentNum - previousNum
  const changePercent = (change / previousNum) * 100
  const sign = change > 0 ? '+' : ''

  return `${sign}${change.toFixed(2)} (${sign}${changePercent.toFixed(2)}%)`
}

// 获取实时金价
async function fetchGoldPrices() {
  loading.prices = true
  error.value = ''
  try {
    goldPrices.value = await goldApi.getGoldPrices()
    console.log('金价数据:', goldPrices.value)
  }
  catch (err) {
    error.value = `获取金价失败: ${err}`
    console.error('获取金价失败:', err)
  }
  finally {
    loading.prices = false
  }
}

// 获取K线数据
async function fetchKLineData() {
  loading.kline = true
  error.value = ''
  try {
    klineData.value = await goldApi.getDaysGoldData()
    console.log('K线数据:', klineData.value)
  }
  catch (err) {
    error.value = `获取K线数据失败: ${err}`
    console.error('获取K线数据失败:', err)
  }
  finally {
    loading.kline = false
  }
}

// 获取分时数据
async function fetchTimeData() {
  loading.time = true
  error.value = ''
  try {
    timeData.value = await goldApi.getTimeGoldData()
    console.log('分时数据:', timeData.value)
  }
  catch (err) {
    error.value = `获取分时数据失败: ${err}`
    console.error('获取分时数据失败:', err)
  }
  finally {
    loading.time = false
  }
}

// 获取技术指标
async function fetchTechnicalData() {
  loading.technical = true
  error.value = ''
  try {
    technicalData.value = await goldApi.getTechnicalIndicators('MA', 14)
    console.log('技术指标数据:', technicalData.value)
  }
  catch (err) {
    error.value = `获取技术指标失败: ${err}`
    console.error('获取技术指标失败:', err)
  }
  finally {
    loading.technical = false
  }
}

// 获取市场新闻
async function fetchNews() {
  loading.news = true
  error.value = ''
  try {
    newsData.value = await goldApi.getMarketNews()
    console.log('新闻数据:', newsData.value)
  }
  catch (err) {
    error.value = `获取新闻失败: ${err}`
    console.error('获取新闻失败:', err)
  }
  finally {
    loading.news = false
  }
}
</script>

<template>
  <view class="gold-demo">
    <view class="header">
      <text class="title">
        黄金价格API演示
      </text>
    </view>

    <view class="section">
      <view class="section-title">
        实时金价
      </view>
      <button :disabled="loading.prices" @click="fetchGoldPrices">
        {{ loading.prices ? '加载中...' : '获取实时金价' }}
      </button>
      <view v-if="goldPrices.length" class="data-list">
        <view v-for="item in goldPrices" :key="item.name" class="price-item">
          <text class="variety">
            {{ item.name }}
          </text>
          <text class="price">
            {{ goldApi.formatPrice(item.currentPrice) }}
          </text>
          <text class="change">
            涨跌: {{ calculateChange(item.currentPrice, item.previousSettlement) }}
          </text>
          <view class="price-details">
            <text class="detail">
              买: {{ goldApi.formatPrice(item.buyPrice) }}
            </text>
            <text class="detail">
              卖: {{ goldApi.formatPrice(item.sellPrice) }}
            </text>
            <text class="detail">
              高: {{ goldApi.formatPrice(item.high) }}
            </text>
            <text class="detail">
              低: {{ goldApi.formatPrice(item.low) }}
            </text>
          </view>
          <text class="update-time">
            更新时间: {{ item.createTime }}
          </text>
        </view>
      </view>
    </view>

    <view class="section">
      <view class="section-title">
        K线数据
      </view>
      <button :disabled="loading.kline" @click="fetchKLineData">
        {{ loading.kline ? '加载中...' : '获取K线数据' }}
      </button>
      <view v-if="klineData.length" class="data-list">
        <view v-for="item in klineData.slice(0, 5)" :key="item._id" class="kline-item">
          <text class="date">
            {{ goldApi.formatDateTime(item.date_time) }}
          </text>
          <text class="ohlc">
            开: {{ goldApi.formatPrice(item.open) }}
            高: {{ goldApi.formatPrice(item.high) }}
            低: {{ goldApi.formatPrice(item.low) }}
            收: {{ goldApi.formatPrice(item.new) }}
          </text>
        </view>
      </view>
    </view>

    <view class="section">
      <view class="section-title">
        分时数据
      </view>
      <button :disabled="loading.time" @click="fetchTimeData">
        {{ loading.time ? '加载中...' : '获取分时数据' }}
      </button>
      <view v-if="timeData.length" class="data-list">
        <view v-for="item in timeData.slice(0, 5)" :key="item.time" class="time-item">
          <text class="time">
            {{ item.time }}
          </text>
          <text class="price">
            {{ goldApi.formatPrice(item.price) }}
          </text>
          <text v-if="item.volume" class="volume">
            成交量: {{ item.volume }}
          </text>
        </view>
      </view>
    </view>

    <view class="section">
      <view class="section-title">
        技术指标
      </view>
      <button :disabled="loading.technical" @click="fetchTechnicalData">
        {{ loading.technical ? '加载中...' : '获取技术指标' }}
      </button>
      <view v-if="technicalData" class="technical-data">
        <text>{{ JSON.stringify(technicalData, null, 2) }}</text>
      </view>
    </view>

    <view class="section">
      <view class="section-title">
        市场新闻
      </view>
      <button :disabled="loading.news" @click="fetchNews">
        {{ loading.news ? '加载中...' : '获取市场新闻' }}
      </button>
      <view v-if="newsData.length" class="data-list">
        <view v-for="(item, index) in newsData.slice(0, 3)" :key="index" class="news-item">
          <text class="news-title">
            {{ item.title || '新闻标题' }}
          </text>
          <text class="news-content">
            {{ item.content || '新闻内容' }}
          </text>
        </view>
      </view>
    </view>

    <view v-if="error" class="error">
      <text>错误: {{ error }}</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.gold-demo {
  padding: 20rpx;

  .header {
    text-align: center;
    margin-bottom: 40rpx;

    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .section {
    margin-bottom: 40rpx;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }

    button {
      width: 100%;
      padding: 20rpx;
      background: #007aff;
      color: white;
      border: none;
      border-radius: 8rpx;
      font-size: 28rpx;
      margin-bottom: 20rpx;

      &:disabled {
        background: #ccc;
      }
    }
  }

  .data-list {
    .price-item,
    .kline-item,
    .time-item,
    .news-item {
      padding: 16rpx;
      background: white;
      border-radius: 8rpx;
      margin-bottom: 12rpx;

      .variety,
      .date,
      .time,
      .news-title {
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 8rpx;
      }

      .price,
      .ohlc,
      .news-content {
        color: #666;
        font-size: 26rpx;
      }

      .change {
        font-size: 24rpx;
        color: #666;
        margin-bottom: 8rpx;

        &.up {
          color: #ff3b30;
        }

        &.down {
          color: #34c759;
        }

        &.neutral {
          color: #666;
        }
      }

      .price-details {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        margin: 8rpx 0;

        .detail {
          font-size: 22rpx;
          color: #666;
          background: #f5f5f5;
          padding: 4rpx 8rpx;
          border-radius: 4rpx;
        }
      }

      .update-time {
        font-size: 20rpx;
        color: #999;
        margin-top: 8rpx;
      }

      .volume {
        color: #999;
        font-size: 24rpx;
      }
    }
  }

  .technical-data {
    background: white;
    padding: 20rpx;
    border-radius: 8rpx;

    text {
      font-family: monospace;
      font-size: 24rpx;
      color: #333;
      white-space: pre-wrap;
    }
  }

  .error {
    padding: 20rpx;
    background: #ffebee;
    border-radius: 8rpx;
    margin-top: 20rpx;

    text {
      color: #d32f2f;
      font-size: 26rpx;
    }
  }
}
</style>
