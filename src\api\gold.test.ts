import { beforeEach, describe, expect, it, vi } from 'vitest'
import { http } from '@/utils/http'
import { GoldAPI } from './gold'

// Mock http utils
vi.mock('@/utils/http', () => ({
  http: {
    get: vi.fn(),
  },
}))

describe('goldAPI', () => {
  let api: GoldAPI

  beforeEach(() => {
    api = new GoldAPI()
    vi.clearAllMocks()
  })

  describe('getGoldPrices', () => {
    it('should fetch gold prices successfully', async () => {
      const mockData = [
        {
          variety: 'AU9999',
          new: 450.5,
          chg: 2.3,
          diffper: 0.51,
          high: 452.0,
          low: 448.0,
          open: 449.0,
          buy: 450.0,
          sell: 451.0,
          date_time: '2024-01-01 10:00:00',
        },
      ]

      const mockResponse = {
        code: 0,
        msg: 'success',
        data: mockData,
      }

      vi.mocked(http.get).mockResolvedValue(mockResponse)

      const result = await api.getGoldPrices()

      expect(http.get).toHaveBeenCalledWith(
        'http://127.0.0.1:8001/open/gold/openGoldPricesController/',
      )
      expect(result).toEqual(mockData)
    })
  })

  describe('getDaysGoldData', () => {
    it('should fetch K-line data successfully', async () => {
      const mockData = [
        {
          _id: '1',
          date_time: '2024-01-01',
          buy: 450.0,
          diffper: 0.5,
          high: 452.0,
          low: 448.0,
          new: 450.5,
          open: 449.0,
          sell: 451.0,
          variety: 'AU9999',
          volume: 1000,
        },
      ]

      const mockResponse = {
        code: 0,
        msg: 'success',
        data: mockData,
      }

      vi.mocked(http.get).mockResolvedValue(mockResponse)

      const result = await api.getDaysGoldData()

      expect(http.get).toHaveBeenCalledWith(
        'http://127.0.0.1:8001/open/gold/openDaysGoldController/',
      )
      expect(result).toEqual(mockData)
    })
  })

  describe('getHistoryData', () => {
    it('should fetch history data with date range', async () => {
      const mockData = [
        {
          _id: '1',
          date_time: '2024-01-01',
          buy: 450.0,
          diffper: 0.5,
          high: 452.0,
          low: 448.0,
          new: 450.5,
          open: 449.0,
          sell: 451.0,
          variety: 'AU9999',
        },
      ]

      const mockResponse = {
        code: 0,
        msg: 'success',
        data: mockData,
      }

      vi.mocked(http.get).mockResolvedValue(mockResponse)

      const result = await api.getHistoryData('2024-01-01', '2024-01-31')

      expect(http.get).toHaveBeenCalledWith(
        'http://127.0.0.1:8001/open/gold/openHistoryGoldController/',
        {
          start_date: '2024-01-01',
          end_date: '2024-01-31',
        },
      )
      expect(result).toEqual(mockData)
    })
  })

  describe('utility methods', () => {
    it('should format price correctly', () => {
      expect(api.formatPrice(450.123)).toBe('¥450.12')
      expect(api.formatPrice('450.567', '$')).toBe('$450.57')
      expect(api.formatPrice('invalid')).toBe('--')
    })

    it('should calculate change percent correctly', () => {
      expect(api.formatChangePercent(5, 100)).toBe('+5.00%')
      expect(api.formatChangePercent(-3, 100)).toBe('-3.00%')
      expect(api.formatChangePercent(0, 100)).toBe('0.00%')
    })

    it('should determine price trend correctly', () => {
      expect(api.getPriceTrend(5)).toBe('up')
      expect(api.getPriceTrend(-3)).toBe('down')
      expect(api.getPriceTrend(0)).toBe('neutral')
    })
  })

  describe('technical indicators', () => {
    it('should calculate moving average correctly', () => {
      const prices = [100, 102, 101, 103, 105]
      const ma3 = api.calculateMA(prices, 3)

      expect(ma3[0]).toBeNaN()
      expect(ma3[1]).toBeNaN()
      expect(ma3[2]).toBeCloseTo(101) // (100+102+101)/3
      expect(ma3[3]).toBeCloseTo(102) // (102+101+103)/3
      expect(ma3[4]).toBeCloseTo(103) // (101+103+105)/3
    })

    it('should calculate RSI correctly', () => {
      const prices = [44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 47.25, 47.92, 46.23, 44.18, 46.57, 46.61, 45.41]
      const rsi = api.calculateRSI(prices, 14)

      expect(rsi).toHaveLength(1) // Only one RSI value for 15 prices with period 14
      expect(rsi[0]).toBeGreaterThan(0)
      expect(rsi[0]).toBeLessThan(100)
    })
  })
})
